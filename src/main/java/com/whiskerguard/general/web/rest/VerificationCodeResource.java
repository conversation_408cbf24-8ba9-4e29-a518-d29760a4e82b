package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.domain.enumeration.VerificationCodeType;
import com.whiskerguard.general.model.VerificationCodeRequest;
import com.whiskerguard.general.model.VerificationCodeResponse;
import com.whiskerguard.general.model.VerificationCodeValidateRequest;
import com.whiskerguard.general.service.VerificationCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 验证码相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21
 */
@RestController
@RequestMapping("/api/verification/code")
public class VerificationCodeResource {

    private static final Logger log = LoggerFactory.getLogger(VerificationCodeResource.class);

    private final VerificationCodeService verificationCodeService;

    @Autowired
    public VerificationCodeResource(VerificationCodeService verificationCodeService) {
        this.verificationCodeService = verificationCodeService;
    }

    /**
     * 方法名称：发送验证码 sendVerificationCode
     * 描述：发送验证码。
     *
     * @param request 验证码请求
     * @return 发送结果
     * @since 1.0
     */
    @PostMapping("/send")
    @Operation(summary = "发送验证码", description = "向指定手机号发送4位数字验证码")
    public ResponseEntity<VerificationCodeResponse> sendVerificationCode(
        @Valid @RequestBody VerificationCodeRequest request) {
        log.debug("REST request to send verification code: phoneNumber={}, codeType={}", request.getPhoneNumber(), request.getCodeType());
        VerificationCodeResponse response = verificationCodeService.sendVerificationCode(request);
        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 方法名称：validateVerificationCode
     * 描述：验证验证码。
     *
     * @param request 验证请求
     * @return 验证结果
     * @since 1.0
     */
    @PostMapping("/validate")
    @Operation(summary = "验证验证码", description = "验证用户输入的验证码是否正确")
    public ResponseEntity<VerificationCodeResponse> validateVerificationCode(
        @Valid @RequestBody VerificationCodeValidateRequest request) {
        log.debug("REST request to validate verification code: phoneNumber={}, codeType=={}", request.getPhoneNumber(), request.getCodeType());

        VerificationCodeResponse response = verificationCodeService.validateVerificationCode(request);
        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 方法名称：canSendVerificationCode
     * 描述：检查是否可以发送验证码。
     *
     * @param phoneNumber 手机号
     * @param codeType    验证码类型
     * @return 是否可以发送
     * @since 1.0
     */
    @GetMapping("/can/send")
    @Operation(summary = "检查是否可以发送验证码", description = "检查指定手机号是否可以发送验证码")
    public ResponseEntity<Map<String, Object>> canSendVerificationCode(
        @Parameter(description = "手机号") @RequestParam String phoneNumber,
        @Parameter(description = "验证码类型") @RequestParam(defaultValue = "LOGIN")
        VerificationCodeType codeType) {
        log.debug("REST request to check can send verification code: phoneNumber={}, codeType={}",
            VerificationCodeResponse.maskPhoneNumber(phoneNumber), codeType);

        boolean canSend = verificationCodeService.canSendVerificationCode(phoneNumber, codeType);
        Map<String, Object> response = new HashMap<>();
        response.put("canSend", canSend);
        response.put("phoneNumber", VerificationCodeResponse.maskPhoneNumber(phoneNumber));
        return ResponseEntity.ok(response);
    }

    /**
     * 方法名称：batchSendVerificationCode
     * 描述：批量发送验证码。
     *
     * @param request 验证码请求
     * @return 发送结果
     * @since 1.0
     */
    @PostMapping("/batch/send")
    @Operation(summary = "批量发送验证码", description = "批量向多个手机号发送验证码（管理员功能）")
    public ResponseEntity<Map<String, Object>> batchSendVerificationCode(
        @RequestBody VerificationCodeRequest request) {
        log.debug("REST request to batch send verification codes: count={}", request.getPhoneNumbers().size());

        Map<String, Object> results = new HashMap<>();
        int successCount = 0;
        int failureCount = 0;

        for (String phoneNumber : request.getPhoneNumbers()) {
            try {
                VerificationCodeRequest codeRequest = new VerificationCodeRequest();
                codeRequest.setPhoneNumber(phoneNumber);
                codeRequest.setCodeType(request.getCodeType());
                VerificationCodeResponse response = verificationCodeService.sendVerificationCode(codeRequest);
                if (response.isSuccess()) {
                    successCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                log.error("批量发送验证码失败: phoneNumber={}", phoneNumber, e);
                failureCount++;
            }
        }
        results.put("message", "批量发送完成");
        results.put("totalCount", request.getPhoneNumbers().size());
        results.put("successCount", successCount);
        results.put("failureCount", failureCount);
        return ResponseEntity.ok(results);
    }

}
