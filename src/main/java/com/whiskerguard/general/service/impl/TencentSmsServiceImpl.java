package com.whiskerguard.general.service.impl;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import com.tencentcloudapi.sms.v20210111.models.SendStatus;
import com.whiskerguard.general.config.ApplicationProperties;
import com.whiskerguard.general.model.NotificationResponse;
import com.whiskerguard.general.model.NotificationType;
import com.whiskerguard.general.model.SmsRequest;
import com.whiskerguard.general.service.SmsService;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 腾讯云短信服务实现
 * 参考腾讯云官方SDK文档实现
 */
@Service("tencentSmsService")
public class TencentSmsServiceImpl implements SmsService {

    private static final Logger log = LoggerFactory.getLogger(TencentSmsServiceImpl.class);

    private final ApplicationProperties applicationProperties;

    @Autowired
    public TencentSmsServiceImpl(ApplicationProperties applicationProperties) {
        this.applicationProperties = applicationProperties;
    }

    @Override
    public NotificationResponse send(SmsRequest request) {
        ApplicationProperties.Notification.Sms.TencentSms tencentSmsProperties = applicationProperties
            .getNotification()
            .getSms()
            .getTencent();

        if (!tencentSmsProperties.isEnabled()) {
            return NotificationResponse.failure("腾讯云短信服务未启用");
        }

        try {
            // 实例化一个认证对象
            Credential credential = new Credential(tencentSmsProperties.getSecretId(), tencentSmsProperties.getSecretKey());

            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setReqMethod("POST");
            // 请求连接超时时间，单位为秒(默认60秒)
            httpProfile.setConnTimeout(30);
            httpProfile.setEndpoint("sms.tencentcloudapi.com");

            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 指定签名算法
            clientProfile.setSignMethod("TC3-HMAC-SHA256");

            // 实例化要请求产品的client对象
            SmsClient client = new SmsClient(credential, tencentSmsProperties.getRegion(), clientProfile);

            // 实例化一个请求对象
            SendSmsRequest sendSmsRequest = new SendSmsRequest();

            // 设置应用ID
            sendSmsRequest.setSmsSdkAppId(tencentSmsProperties.getAppId());

            // 设置签名
            sendSmsRequest.setSignName(tencentSmsProperties.getSignName());

            // 设置模板ID
            sendSmsRequest.setTemplateId(request.getTemplateId());

            // 设置手机号码
            String[] phoneNumbers = { "+".concat(request.getRegionCode()).concat(request.getRecipient()) };
            sendSmsRequest.setPhoneNumberSet(phoneNumbers);

            // 设置模板参数
            String[] templateParams = convertMapToArray(request.getTemplateParams());
            sendSmsRequest.setTemplateParamSet(templateParams);

            // 发送短信
            SendSmsResponse response = client.SendSms(sendSmsRequest);

            // 处理响应
            SendStatus[] sendStatusSet = response.getSendStatusSet();
            if (sendStatusSet != null && sendStatusSet.length > 0) {
                SendStatus status = sendStatusSet[0];
                if ("Ok".equals(status.getCode())) {
                    NotificationResponse notificationResponse = NotificationResponse.success("短信发送成功", status.getSerialNo());
                    notificationResponse.setType(NotificationType.SMS);
                    return notificationResponse;
                } else {
                    log.error("腾讯云短信发送失败: {}, {}", status.getCode(), status.getMessage());
                    NotificationResponse notificationResponse = NotificationResponse.failure(status.getMessage(), status.getCode());
                    notificationResponse.setType(NotificationType.SMS);
                    return notificationResponse;
                }
            } else {
                log.error("腾讯云短信发送失败: 无响应状态");
                return NotificationResponse.failure("短信发送失败: 无响应状态");
            }
        } catch (TencentCloudSDKException e) {
            log.error("腾讯云短信发送异常", e);
            return NotificationResponse.failure("短信发送异常: " + e.getMessage());
        }
    }

    /**
     * 将Map转换为字符串数组
     */
    private String[] convertMapToArray(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return new String[0];
        }

        List<String> paramList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            paramList.add(String.valueOf(entry.getValue()));
        }

        return paramList.toArray(new String[0]);
    }
}
